<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A curated collection of hydrology, meteorological, and ecological data sources for researchers and scientists.">
    <meta name="keywords" content="hydrology, meteorological data, ecological data, research, datasets, PRISM, IMERG, USGS">
    <meta name="author" content="Hydrology Research Lab">
    
    <!-- Open Graph meta tags for social sharing -->
    <meta property="og:title" content="Hydrology Data Source Finder">
    <meta property="og:description" content="Find and explore essential datasets for hydrological research">
    <meta property="og:type" content="website">
    
    <title>Hydrology Data Source Finder</title>
    
    <!-- Favicon (optional - you can add your institution's favicon) -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- CSS will be embedded here for Google Sites compatibility -->
    <style>
        /* CSS styles will be added in Task 4 */
    </style>
</head>

<body>
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Main application container -->
    <div id="app-container">
        
        <!-- Header section with title and navigation controls -->
        <header class="header" role="banner">
            <div class="header-content">
                <h1 class="main-title">Hydrology Data Source Finder</h1>
                <p class="subtitle">A curated collection of essential datasets for hydrological research</p>
                
                <!-- Search and filter controls -->
                <div class="controls-container" role="search">
                    <div class="search-wrapper">
                        <label for="search-input" class="visually-hidden">Search data sources</label>
                        <input 
                            type="search" 
                            id="search-input" 
                            placeholder="Search by name, description, or keywords..."
                            aria-describedby="search-help"
                            autocomplete="off"
                        >
                        <div id="search-help" class="visually-hidden">
                            Search across data source names, descriptions, and keywords
                        </div>
                    </div>
                    
                    <div class="filter-wrapper">
                        <label for="category-filter" class="visually-hidden">Filter by category</label>
                        <select id="category-filter" aria-describedby="filter-help">
                            <option value="all">All Categories</option>
                            <!-- Options will be populated dynamically by JavaScript -->
                        </select>
                        <div id="filter-help" class="visually-hidden">
                            Filter data sources by category type
                        </div>
                    </div>
                    
                    <!-- Results counter -->
                    <div class="results-info" aria-live="polite">
                        <span id="results-count">Loading...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main content area -->
        <main id="main-content" class="main-content" role="main">
            
            <!-- Loading indicator -->
            <div id="loader" class="loader" aria-live="polite">
                <div class="loader-spinner" aria-hidden="true"></div>
                <p class="loader-text">Loading data sources from Google Sheets...</p>
            </div>
            
            <!-- Error message container -->
            <div id="error-container" class="error-container hidden" role="alert">
                <h2>Unable to Load Data</h2>
                <p id="error-message">There was a problem loading the data sources. Please try refreshing the page.</p>
                <button id="retry-button" class="retry-button">Try Again</button>
            </div>
            
            <!-- Data container where cards will be displayed -->
            <div id="data-container" class="data-container">
                <!-- Data cards will be dynamically inserted here by JavaScript -->
            </div>
            
            <!-- No results message -->
            <div id="no-results" class="no-results hidden">
                <h2>No Data Sources Found</h2>
                <p>Try adjusting your search terms or category filter to find relevant data sources.</p>
                <button id="clear-filters" class="clear-filters-button">Clear All Filters</button>
            </div>
            
        </main>
        
        <!-- Footer section -->
        <footer class="footer" role="contentinfo">
            <div class="footer-content">
                <p class="footer-text">
                    <strong>Hydrology Data Source Finder</strong> | 
                    Managed by [Your Research Lab/Institution Name]
                </p>
                <p class="footer-details">
                    Data stored in Google Sheets | 
                    Last updated: <span id="last-updated">Loading...</span>
                </p>
                <p class="footer-links">
                    <a href="#" id="about-link">About This Project</a> | 
                    <a href="#" id="contact-link">Contact</a> | 
                    <a href="#" id="suggest-data-link">Suggest a Data Source</a>
                </p>
            </div>
        </footer>
        
    </div>
    
    <!-- Modal for additional information (optional) -->
    <div id="info-modal" class="modal hidden" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">About This Project</h2>
                <button id="modal-close" class="modal-close" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>This website catalogs essential data sources for hydrology research. The data is maintained in Google Sheets and updated regularly by our research team.</p>
                <p>If you have suggestions for additional data sources or notice any errors, please contact us.</p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript will be embedded here for Google Sites compatibility -->
    <script>
        // JavaScript code will be added in Task 5
        
        // Configuration - Update this with your actual API URL
        const API_CONFIG = {
            url: 'https://script.google.com/macros/s/AKfycbwkLTh5XqfPrY9JqF3s9FoYE9jzFAtl5_CJwdcTiZJeE9wrvF1RDItUfui6QnbS6X3Dhw/exec',
            timeout: 10000 // 10 seconds
        };
        
        // Global variables for application state
        let allDataSources = [];
        let filteredDataSources = [];
        let currentCategory = 'all';
        let currentSearchTerm = '';
        
        // DOM element references (will be used in Task 5)
        const elements = {
            loader: document.getElementById('loader'),
            errorContainer: document.getElementById('error-container'),
            dataContainer: document.getElementById('data-container'),
            noResults: document.getElementById('no-results'),
            searchInput: document.getElementById('search-input'),
            categoryFilter: document.getElementById('category-filter'),
            resultsCount: document.getElementById('results-count'),
            retryButton: document.getElementById('retry-button'),
            clearFiltersButton: document.getElementById('clear-filters'),
            lastUpdated: document.getElementById('last-updated')
        };
        
        // Placeholder for initialization - will be implemented in Task 5
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded - ready for JavaScript implementation');
        });
    </script>
    
</body>
</html>
